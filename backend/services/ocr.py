import os
import io
import json
import traceback
from flask import current_app
from datetime import datetime
from models.models import db, Invoice, LineItem
from utils.helpers import (
    extract_invoice_number,
    extract_vendor_name,
    extract_date,
    extract_total_amount,
    extract_line_items
)
from services.mindee_service import MindeeService
from services.mindee_client_service import MindeeClientService

class OCRService:
    """Service class for OCR operations using Mindee API."""

    @staticmethod
    def process_image(file_path, user_id, vendor='generic'):
        """Process an image using OCR (Mindee API).

        Args:
            file_path (str): Path to the image file
            user_id (str): ID of the user who uploaded the file
            vendor (str): Vendor type for custom OCR processing (bova, kast, dekalb, or generic)

        Returns:
            tuple: (invoice_data, ocr_text, message, status_code)
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                current_app.logger.error(f"File not found at path: {file_path}")
                return None, "", f"File not found at path: {file_path}", 400

            current_app.logger.info(f"Processing image file: {file_path}")

            # Use Mindee API for invoice processing
            try:
                current_app.logger.info(f"Using Mindee API for invoice processing (vendor: {vendor})")

                # Use the Mindee direct API service (more reliable than client library)
                current_app.logger.info("Using Mindee direct API calls")
                mindee_service = MindeeService()
                mindee_result = mindee_service.process_invoice(file_path, vendor)

                # Debug: Log the complete Mindee result for Bova vendor
                if vendor.lower() == 'bova':
                    current_app.logger.info(f"BOVA OCR DEBUG - Complete Mindee result: {mindee_result}")
                    current_app.logger.info(f"BOVA OCR DEBUG - Invoice number: {mindee_result.get('invoice_number')}")
                    current_app.logger.info(f"BOVA OCR DEBUG - Date: {mindee_result.get('date')}")
                    current_app.logger.info(f"BOVA OCR DEBUG - Total amount: {mindee_result.get('total_amount')}")
                    current_app.logger.info(f"BOVA OCR DEBUG - Vendor/Supplier: {mindee_result.get('vendor')} / {mindee_result.get('supplier_name')}")
                    if mindee_result.get('items'):
                        current_app.logger.info(f"BOVA OCR DEBUG - First line item: {mindee_result.get('items')[0] if mindee_result.get('items') else 'None'}")

                if not mindee_result.get("success"):
                    current_app.logger.error("Mindee API processing failed.")
                    return None, "", "OCR processing failed with Mindee API. Please try again or contact support.", 500

                current_app.logger.info(f"Successfully processed invoice with Mindee API. Found {len(mindee_result.get('items', []))} line items")

                # Use a placeholder for the raw text since we have structured data from Mindee
                full_text = "Using Mindee structured data for invoice processing."
                mean_confidence = 0.9  # High confidence for Mindee API
                ocr_json = json.dumps(mindee_result)

                # Use Mindee data for invoice details
                invoice_number = mindee_result.get("invoice_number") or extract_invoice_number(full_text)

                # Get vendor name from Mindee result - check both "vendor" and "supplier_name" fields
                # If using a custom vendor API, use the vendor parameter directly
                if vendor.lower() in ['bova', 'kast', 'dekalb']:
                    # For custom vendor APIs, use the vendor parameter as the vendor name
                    vendor_name = mindee_result.get("vendor") or vendor.capitalize()
                else:
                    # For generic API, try to get supplier_name from the result
                    vendor_name = mindee_result.get("supplier_name") or mindee_result.get("vendor") or extract_vendor_name(full_text)

                # Convert date string from Mindee to Python date object
                if mindee_result.get("date"):
                    try:
                        # Try different date formats
                        date_str = mindee_result.get("date")
                        for fmt in ["%Y-%m-%d", "%m/%d/%Y", "%d/%m/%Y", "%Y/%m/%d"]:
                            try:
                                invoice_date = datetime.strptime(date_str, fmt).date()
                                break
                            except ValueError:
                                continue
                        # If none of the formats worked, fall back to extract_date
                        if 'invoice_date' not in locals() or invoice_date is None:
                            invoice_date = extract_date(full_text)
                    except Exception as date_error:
                        current_app.logger.warning(f"Error converting Mindee date: {date_error}")
                        invoice_date = extract_date(full_text)
                else:
                    invoice_date = extract_date(full_text)

                total_amount = mindee_result.get("total_amount") or extract_total_amount(full_text)

                # Use Mindee line items
                line_items_data = mindee_result.get("items", [])

                # Set OCR source
                ocr_source = "Mindee API"

                current_app.logger.info(f"Using Mindee API data: Number={invoice_number}, Vendor={vendor_name}, Date={invoice_date}, Total={total_amount}, Items={len(line_items_data)}")

            except Exception as mindee_error:
                current_app.logger.error(f"Error processing with Mindee API: {str(mindee_error)}")
                return None, "", f"OCR processing failed: {str(mindee_error)}", 500

            # Create a dictionary with invoice data instead of saving to database
            invoice_data = {
                'invoice_number': invoice_number or "Unknown",
                'vendor_name': vendor_name or "Unknown Vendor",
                'invoice_date': invoice_date,
                'total_amount': total_amount or 0.0,
                'original_filename': os.path.basename(file_path),
                'file_path': file_path,
                'line_items': []
            }

            # Add line items if any were extracted
            for item_data in line_items_data:
                try:
                    # Validate line item data
                    description = item_data.get('description', '')
                    if not description or len(description) > 500:
                        description = description[:500] if description else "Unknown Item"

                    # Ensure numeric values are valid
                    quantity = item_data.get('quantity', 0)
                    unit_price = item_data.get('unit_price', 0)
                    amount = item_data.get('amount', 0)

                    # Convert to float and handle invalid values
                    try:
                        quantity = float(quantity) if quantity is not None else 0
                        if not isinstance(quantity, (int, float)) or quantity > 1000000:
                            quantity = 0
                    except (ValueError, TypeError):
                        quantity = 0

                    try:
                        unit_price = float(unit_price) if unit_price is not None else 0
                        if not isinstance(unit_price, (int, float)) or unit_price > 1000000:
                            unit_price = 0
                    except (ValueError, TypeError):
                        unit_price = 0

                    try:
                        amount = float(amount) if amount is not None else 0
                        if not isinstance(amount, (int, float)) or amount > 1000000:
                            amount = 0
                    except (ValueError, TypeError):
                        amount = 0

                    # Add the line item to the invoice data
                    line_item = {
                        'description': description,
                        'quantity': quantity,
                        'unit_price': unit_price,
                        'amount': amount
                    }
                    invoice_data['line_items'].append(line_item)
                    current_app.logger.info(f"Added line item: {description}, qty: {quantity}, price: {unit_price}, amount: {amount}")
                except Exception as item_error:
                    current_app.logger.error(f"Error adding line item: {str(item_error)}")
                    # Continue processing other line items

            # Include OCR source in the success message
            success_message = f"Invoice processed successfully using {ocr_source}"
            return invoice_data, full_text, success_message, 200

        except Exception as e:
            db.session.rollback()
            error_details = traceback.format_exc()
            current_app.logger.error(f"Error processing OCR: {str(e)}")
            current_app.logger.error(f"Traceback: {error_details}")
            return None, "", f"Error processing OCR: {str(e)}", 500



    @staticmethod
    def get_latest_ocr_result(user_id, min_confidence=0):
        """Get the latest OCR result for a user.

        Args:
            user_id (str): ID of the user
            min_confidence (float): Minimum confidence threshold (0-1)

        Returns:
            tuple: (invoice, message, status_code)
        """
        try:
            query = Invoice.query.filter_by(user_id=user_id)

            # Apply confidence filter if specified
            if min_confidence > 0:
                query = query.filter(Invoice.confidence >= min_confidence)

            # Get the latest invoice that meets the criteria
            invoice = query.order_by(Invoice.created_at.desc()).first()

            if not invoice:
                if min_confidence > 0:
                    return None, f"No OCR results found with confidence >= {min_confidence}", 404
                else:
                    return None, "No OCR results found", 404

            return invoice, "Latest OCR result retrieved successfully", 200

        except Exception as e:
            error_details = traceback.format_exc()
            current_app.logger.error(f"Error retrieving latest OCR result: {str(e)}")
            current_app.logger.error(f"Traceback: {error_details}")
            return None, f"Error retrieving latest OCR result: {str(e)}", 500
